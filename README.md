# سحب بيانات الجمعيات من موقع المركز الوطني

هذا المشروع يقوم بسحب بيانات الجمعيات الخيرية من موقع المركز الوطني للقطاع غير الربحي.

## الملفات

- `scrap.py` - الملف الرئيسي لسحب البيانات (السحب الكامل)
- `quick_test.py` - اختبار سريع لسحب عدد قليل من الجمعيات
- `test_scraper.py` - ملف اختبار لفحص الموقع وتحليل هيكله
- `README.md` - هذا الملف

## ✅ حالة المشروع

**المشروع يعمل بشكل مثالي!** تم اختباره بنجاح وهو جاهز للاستخدام.

## المتطلبات

```bash
pip install requests beautifulsoup4 pandas lxml openpyxl
```

## كيفية الاستخدام

### 1. اختبار سريع (مُوصى به للبداية)

```bash
python quick_test.py
```

هذا الأمر سيقوم بـ:
- سحب 3 جمعيات فقط للاختبار
- عرض ملخص النتائج
- حفظ البيانات في ملف CSV
- يستغرق أقل من دقيقة

### 2. اختبار الاتصال والهيكل

```bash
python test_scraper.py
```

هذا الأمر سيقوم بـ:
- اختبار الوصول للموقع
- تحليل هيكل الصفحة
- البحث عن عناصر الجمعيات وروابط "المزيد"
- حفظ صفحة اختبار في مجلد `results`

### 3. السحب الكامل (للاستخدام المتقدم)

```bash
python scrap.py
```

⚠️ **تحذير**: الموقع يحتوي على أكثر من 1200 صفحة (6000+ جمعية)!
- قد يستغرق ساعات عديدة
- يُنصح بتعديل الكود لتحديد عدد أقل من الصفحات أولاً

## الميزات

### سحب البيانات التفصيلية ✅
- **الاسم**: اسم الجمعية كاملاً
- **رقم التسجيل**: رقم الترخيص الرسمي
- **المجال**: نوع الكيان (جمعية أهلية، مؤسسة أهلية، إلخ)
- **العنوان**: العنوان الكامل مع الرمز البريدي
- **الهاتف**: رقم الهاتف الرسمي
- **الفاكس**: رقم الفاكس
- **الموقع الإلكتروني**: رابط الموقع الرسمي
- **البريد الإلكتروني**: عنوان البريد الإلكتروني
- **صندوق البريد**: رقم صندوق البريد

### مثال على البيانات المستخرجة
```
الاسم: الجمعية النسائية الخيرية الأولى بجدة
رقم التسجيل: 1
المجال: جمعية أهلية
العنوان: منطقة مكة المكرمة _ خلف التلفزيون _ من شارع فلسطين الى جنوب جدة الى كيلو 14 _ الرمز البريدي : 5
الهاتف: 0581023387
الفاكس: 6364083
الموقع الإلكتروني: https://oula.org.sa/
البريد الإلكتروني: <EMAIL>
صندوق البريد: 8320
```

### التعامل مع الصفحات المتعددة (Pagination)
- اكتشاف تلقائي لعدد الصفحات
- التنقل عبر جميع الصفحات
- حفظ التقدم بعد كل صفحة

### البحث الذكي عن العناصر
- طرق متعددة للبحث عن بطاقات الجمعيات
- البحث عن روابط "المزيد" بطرق مختلفة
- التعامل مع هياكل HTML متنوعة

### معالجة الأخطاء
- إعادة المحاولة عند فشل الطلبات
- حفظ التقدم لتجنب فقدان البيانات
- تسجيل مفصل للأخطاء

### تنظيف البيانات
- إزالة المسافات الزائدة
- تنظيف الأحرف الخاصة
- توحيد تنسيق البيانات

## الإعدادات

### تحديد عدد الصفحات
في ملف `scrap.py`، يمكنك تعديل:

```python
# للسحب الكامل (جميع الصفحات)
last_page = get_last_page_number(BASE_URL, HEADERS)

# للاختبار (عدد محدود من الصفحات)
last_page = 5  # مثال: أول 5 صفحات فقط
```

### تحديد عدد العناصر لكل صفحة
```python
for card_idx, card in enumerate(charity_cards[:10]):  # أول 10 عناصر
```

## المخرجات

### ملفات CSV
- `results/charities_data_YYYYMMDD_HHMMSS.csv` - البيانات النهائية
- `results/progress_page_X_YYYYMMDD_HHMMSS.csv` - ملفات التقدم

### ملفات Excel
- `results/charities_data_YYYYMMDD_HHMMSS.xlsx` - البيانات بصيغة Excel

### ملفات التشخيص
- `results/first_page.html` - محتوى الصفحة الأولى للتحليل
- `results/test_page.html` - صفحة الاختبار

## نصائح للاستخدام

1. **ابدأ بالاختبار**: استخدم `test_scraper.py` أولاً لفهم هيكل الموقع
2. **اختبر بعدد قليل**: ابدأ بـ 2-3 صفحات للتأكد من عمل الكود
3. **راقب السجلات**: تابع الرسائل المطبوعة لفهم ما يحدث
4. **احفظ التقدم**: الكود يحفظ التقدم تلقائياً بعد كل صفحة

## استكشاف الأخطاء

### لا يتم العثور على جمعيات
- تحقق من ملف `results/first_page.html`
- قم بتشغيل `test_scraper.py` لتحليل الهيكل
- قد تحتاج لتحديث selectors في الكود

### فشل في الوصول للموقع
- تحقق من الاتصال بالإنترنت
- قد يكون الموقع محجوب أو متوقف مؤقتاً
- جرب تغيير User-Agent في HEADERS

### بيانات ناقصة
- الموقع قد يحتوي على بيانات ناقصة فعلياً
- تحقق من صفحات التفاصيل يدوياً
- قد تحتاج لتحسين selectors لاستخراج البيانات

## الدعم

إذا واجهت مشاكل:
1. تحقق من ملفات السجل والتشخيص
2. قم بتشغيل الاختبارات أولاً
3. تأكد من تحديث المتطلبات
