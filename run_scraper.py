#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل رئيسي لسحب بيانات الجمعيات مع خيارات متعددة
"""

import os
import sys
import subprocess

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("🏛️  سحب بيانات الجمعيات من موقع المركز الوطني للقطاع غير الربحي")
    print("=" * 70)
    print()

def print_menu():
    """طباعة قائمة الخيارات"""
    print("اختر نوع العملية:")
    print()
    print("1️⃣  اختبار سريع (3 جمعيات فقط - مُوصى به للبداية)")
    print("2️⃣  اختبار الاتصال وتحليل هيكل الموقع")
    print("3️⃣  سحب عدد محدود من الصفحات (يمكنك تحديد العدد)")
    print("4️⃣  السحب الكامل (جميع الجمعيات - قد يستغرق ساعات)")
    print("5️⃣  عرض آخر النتائج المحفوظة")
    print("6️⃣  تنظيف ملفات النتائج")
    print("0️⃣  خروج")
    print()

def run_command(command, description):
    """تشغيل أمر مع عرض الوصف"""
    print(f"🚀 {description}")
    print("-" * 50)
    
    try:
        # التأكد من تفعيل البيئة الافتراضية واستخدام python3
        if os.path.exists('venv/bin/activate'):
            command = f"source venv/bin/activate && {command.replace('python', 'python3')}"
        else:
            command = command.replace('python', 'python3')
        
        result = subprocess.run(command, shell=True, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ تم تنفيذ {description} بنجاح!")
        else:
            print(f"❌ حدث خطأ أثناء تنفيذ {description}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print()
    input("اضغط Enter للمتابعة...")

def show_results():
    """عرض آخر النتائج المحفوظة"""
    results_dir = "results"
    
    if not os.path.exists(results_dir):
        print("❌ لا توجد نتائج محفوظة بعد")
        return
    
    files = [f for f in os.listdir(results_dir) if f.endswith('.csv')]
    
    if not files:
        print("❌ لا توجد ملفات CSV في مجلد النتائج")
        return
    
    # ترتيب الملفات حسب تاريخ التعديل
    files.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
    
    print("📁 آخر النتائج المحفوظة:")
    print("-" * 50)
    
    for i, file in enumerate(files[:5], 1):  # عرض آخر 5 ملفات
        file_path = os.path.join(results_dir, file)
        file_size = os.path.getsize(file_path)
        
        # قراءة عدد الأسطر (عدد الجمعيات)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = len(f.readlines()) - 1  # طرح سطر العناوين
        except:
            lines = "غير معروف"
        
        print(f"{i}. {file}")
        print(f"   📊 عدد الجمعيات: {lines}")
        print(f"   📏 حجم الملف: {file_size:,} بايت")
        print()

def clean_results():
    """تنظيف ملفات النتائج"""
    results_dir = "results"
    
    if not os.path.exists(results_dir):
        print("❌ لا يوجد مجلد نتائج للتنظيف")
        return
    
    files = os.listdir(results_dir)
    
    if not files:
        print("❌ مجلد النتائج فارغ بالفعل")
        return
    
    print(f"📁 تم العثور على {len(files)} ملف في مجلد النتائج")
    
    confirm = input("⚠️  هل أنت متأكد من حذف جميع الملفات؟ (اكتب 'نعم' للتأكيد): ")
    
    if confirm.lower() in ['نعم', 'yes', 'y']:
        try:
            for file in files:
                os.remove(os.path.join(results_dir, file))
            print("✅ تم حذف جميع الملفات بنجاح")
        except Exception as e:
            print(f"❌ خطأ أثناء الحذف: {e}")
    else:
        print("❌ تم إلغاء عملية الحذف")

def run_custom_pages():
    """تشغيل سحب عدد محدود من الصفحات"""
    print("📝 سحب عدد محدود من الصفحات")
    print("-" * 50)
    
    try:
        num_pages = int(input("كم صفحة تريد سحبها؟ (كل صفحة تحتوي على 5 جمعيات): "))
        
        if num_pages <= 0:
            print("❌ يجب أن يكون العدد أكبر من صفر")
            return
        
        if num_pages > 100:
            confirm = input(f"⚠️  {num_pages} صفحة تعني {num_pages * 5} جمعية تقريباً. هل أنت متأكد؟ (نعم/لا): ")
            if confirm.lower() not in ['نعم', 'yes', 'y']:
                print("❌ تم إلغاء العملية")
                return
        
        # تعديل ملف scrap.py مؤقتاً
        print(f"🔧 جاري تحديد عدد الصفحات إلى {num_pages}...")
        
        # قراءة الملف الأصلي
        with open('scrap.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # حفظ نسخة احتياطية
        with open('scrap_backup.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # تعديل السطر المطلوب
        import re
        pattern = r'last_page = \d+'
        replacement = f'last_page = {num_pages}'
        
        # البحث عن السطر وتعديله
        if 'last_page = min(last_page, 10)' in content:
            content = content.replace('last_page = min(last_page, 10)', f'last_page = min(last_page, {num_pages})')
        elif '# last_page = min(last_page, 10)' in content:
            content = content.replace('# last_page = min(last_page, 10)', f'last_page = min(last_page, {num_pages})')
        else:
            # إضافة السطر بعد get_last_page_number
            content = re.sub(
                r'(last_page = get_last_page_number\(BASE_URL, HEADERS\))',
                f'\\1\n    last_page = min(last_page, {num_pages})  # تحديد مؤقت',
                content
            )
        
        # كتابة الملف المعدل
        with open('scrap.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # تشغيل السحب
        run_command('python scrap.py', f'سحب {num_pages} صفحة')
        
        # استعادة الملف الأصلي
        with open('scrap_backup.py', 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        with open('scrap.py', 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # حذف النسخة الاحتياطية
        os.remove('scrap_backup.py')
        
        print("✅ تم استعادة الملف الأصلي")
        
    except ValueError:
        print("❌ يرجى إدخال رقم صحيح")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    while True:
        print_header()
        print_menu()
        
        try:
            choice = input("اختر رقم العملية: ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام البرنامج!")
                break
                
            elif choice == '1':
                run_command('python quick_test.py', 'الاختبار السريع')
                
            elif choice == '2':
                run_command('python test_scraper.py', 'اختبار الاتصال وتحليل الهيكل')
                
            elif choice == '3':
                run_custom_pages()
                
            elif choice == '4':
                print("⚠️  تحذير: السحب الكامل قد يستغرق ساعات عديدة!")
                confirm = input("هل أنت متأكد؟ (اكتب 'نعم' للتأكيد): ")
                
                if confirm.lower() in ['نعم', 'yes', 'y']:
                    run_command('python scrap.py', 'السحب الكامل')
                else:
                    print("❌ تم إلغاء السحب الكامل")
                    input("اضغط Enter للمتابعة...")
                    
            elif choice == '5':
                show_results()
                input("اضغط Enter للمتابعة...")
                
            elif choice == '6':
                clean_results()
                input("اضغط Enter للمتابعة...")
                
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
                input("اضغط Enter للمتابعة...")
                
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
