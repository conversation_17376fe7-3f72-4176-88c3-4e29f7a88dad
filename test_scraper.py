#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار محسن لسحب البيانات من موقع الجمعيات
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import os
from datetime import datetime

# --- الإعدادات الأساسية ---
BASE_URL = "https://es.ncnp.gov.sa/v5/nonprofits"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
    'Referer': 'https://es.ncnp.gov.sa/',
    'Sec-Ch-Ua': '"Google Chrome";v="123", "Not:A=Brand";v="8", "Chromium";v="123"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1'
}

def test_page_access():
    """اختبار الوصول للصفحة الرئيسية"""
    print("اختبار الوصول للصفحة الرئيسية...")
    try:
        response = requests.get(BASE_URL, headers=HEADERS, timeout=20)
        response.raise_for_status()
        print(f"✓ تم الوصول بنجاح - كود الاستجابة: {response.status_code}")
        print(f"✓ حجم المحتوى: {len(response.content)} بايت")
        
        # حفظ الصفحة للفحص
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        
        with open('results/test_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("✓ تم حفظ الصفحة في results/test_page.html")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في الوصول للصفحة: {e}")
        return False

def analyze_page_structure():
    """تحليل هيكل الصفحة لفهم العناصر"""
    print("\nتحليل هيكل الصفحة...")
    try:
        response = requests.get(BASE_URL, headers=HEADERS, timeout=20)
        soup = BeautifulSoup(response.content, 'lxml')
        
        # البحث عن العناصر المختلفة
        print("البحث عن عناصر الجمعيات...")
        
        # طرق مختلفة للبحث
        selectors = [
            ('div.search-card-result', 'بطاقات النتائج'),
            ('div[class*="card"]', 'البطاقات العامة'),
            ('div[class*="result"]', 'عناصر النتائج'),
            ('div[class*="item"]', 'العناصر'),
            ('.card', 'البطاقات'),
            ('.result-item', 'عناصر النتائج'),
            ('a[href*="nonprofit"]', 'روابط الجمعيات'),
            ('a[href*="detail"]', 'روابط التفاصيل')
        ]
        
        for selector, description in selectors:
            elements = soup.select(selector)
            print(f"  {description}: {len(elements)} عنصر")
            if elements and len(elements) > 0:
                print(f"    مثال على المحتوى: {str(elements[0])[:100]}...")
        
        # البحث عن روابط "المزيد"
        print("\nالبحث عن روابط 'المزيد'...")
        more_patterns = [
            (re.compile(r'\s*المزيد\s*'), 'المزيد'),
            (re.compile(r'\s*اقرأ المزيد\s*'), 'اقرأ المزيد'),
            (re.compile(r'\s*التفاصيل\s*'), 'التفاصيل'),
            (re.compile(r'\s*عرض\s*'), 'عرض'),
            (re.compile(r'\s*more\s*', re.IGNORECASE), 'more')
        ]
        
        for pattern, name in more_patterns:
            links = soup.find_all('a', text=pattern)
            print(f"  روابط '{name}': {len(links)}")
            if links:
                print(f"    مثال: {links[0].get('href', 'بدون رابط')}")
        
        # البحث عن الترقيم
        print("\nالبحث عن عناصر الترقيم...")
        pagination_selectors = [
            ('ul.pagination', 'قائمة الترقيم'),
            ('.pagination', 'عناصر الترقيم'),
            ('.page-numbers', 'أرقام الصفحات'),
            ('a[href*="page"]', 'روابط الصفحات')
        ]
        
        for selector, description in pagination_selectors:
            elements = soup.select(selector)
            print(f"  {description}: {len(elements)} عنصر")
            if elements:
                print(f"    محتوى: {elements[0].get_text()[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في تحليل الصفحة: {e}")
        return False

def test_detail_page():
    """اختبار الوصول لصفحة تفاصيل جمعية"""
    print("\nاختبار الوصول لصفحة التفاصيل...")
    
    # مثال على رابط تفاصيل (يجب تحديثه بناءً على الموقع الفعلي)
    test_detail_url = f"{BASE_URL}/1"  # مثال
    
    try:
        response = requests.get(test_detail_url, headers=HEADERS, timeout=20)
        if response.status_code == 200:
            print(f"✓ تم الوصول لصفحة التفاصيل بنجاح")
            
            soup = BeautifulSoup(response.content, 'lxml')
            
            # البحث عن عناصر البيانات
            print("البحث عن عناصر البيانات...")
            
            # العنوان
            title_selectors = ['h1', 'h2', 'h3', 'h4.title', '.title']
            for selector in title_selectors:
                element = soup.select_one(selector)
                if element:
                    print(f"  العنوان ({selector}): {element.get_text()[:50]}...")
                    break
            
            # المعلومات
            info_selectors = ['.col-sm-6', '.col-md-6', '.info-item', 'table tr']
            for selector in info_selectors:
                elements = soup.select(selector)
                print(f"  عناصر المعلومات ({selector}): {len(elements)}")
                if elements and len(elements) > 0:
                    print(f"    مثال: {elements[0].get_text()[:50]}...")
            
            return True
        else:
            print(f"✗ لم يتم الوصول لصفحة التفاصيل - كود: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في اختبار صفحة التفاصيل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("اختبار سحب البيانات من موقع الجمعيات")
    print("=" * 60)
    
    # اختبار الوصول للصفحة
    if not test_page_access():
        print("فشل في الوصول للصفحة الرئيسية")
        return
    
    # تحليل هيكل الصفحة
    if not analyze_page_structure():
        print("فشل في تحليل هيكل الصفحة")
        return
    
    # اختبار صفحة التفاصيل
    test_detail_page()
    
    print("\n" + "=" * 60)
    print("انتهى الاختبار")
    print("=" * 60)

if __name__ == "__main__":
    main()
