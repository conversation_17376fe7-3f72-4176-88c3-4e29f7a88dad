#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لسحب عدد قليل من الجمعيات
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import os
from datetime import datetime

# --- الإعدادات الأساسية ---
BASE_URL = "https://es.ncnp.gov.sa/v5/nonprofits"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
    'Referer': 'https://es.ncnp.gov.sa/',
}

def clean_text(text):
    """تنظيف النصوص من المسافات الزائدة والأحرف غير المرغوبة"""
    if not text or text == 'غير متوفر':
        return 'غير متوفر'
    
    text = re.sub(r'\s+', ' ', text.strip())
    text = re.sub(r'[^\w\s\u0600-\u06FF@.\-+():/]', '', text)
    return text.strip() if text.strip() else 'غير متوفر'

def scrape_details(detail_url, headers):
    """سحب البيانات التفصيلية من صفحة الجمعية"""
    try:
        print(f"  -> جاري سحب تفاصيل من: {detail_url}")
        response = requests.get(detail_url, headers=headers, timeout=20)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'lxml')
        
        charity_info = {}

        # سحب اسم الجمعية
        name_element = soup.find('div', class_='card-title')
        if not name_element:
            name_element = soup.select_one('.main-info-details h5')
        if not name_element:
            name_element = soup.find('h5') or soup.find('h4')
        
        if name_element:
            name_text = name_element.get_text()
            name_text = re.sub(r'جمعية أهلية|مؤسسة أهلية|صندوق عائلة', '', name_text).strip()
            charity_info['الاسم'] = clean_text(name_text)
        else:
            charity_info['الاسم'] = 'غير متوفر'

        # سحب النوع/المجال
        category_element = soup.find('span', class_='tag')
        if not category_element:
            category_element = soup.find('span', class_='light tag')
        charity_info['المجال'] = clean_text(category_element.text) if category_element else 'غير متوفر'
        
        # سحب رقم الترخيص
        license_text = soup.get_text()
        reg_num_match = re.search(r'رقم\s*الترخيص\s*:?\s*(\d+)', license_text)
        if not reg_num_match:
            reg_num_match = re.search(r'رقم\s*التسجيل\s*:?\s*(\d+)', license_text)
        charity_info['رقم التسجيل'] = reg_num_match.group(1) if reg_num_match else 'غير متوفر'
        
        # سحب العنوان
        address_paragraphs = soup.find_all('p')
        for p in address_paragraphs:
            text = p.get_text()
            if 'منطقة' in text or 'الرمز البريدي' in text:
                charity_info['العنوان'] = clean_text(text)
                break
        
        # سحب معلومات الاتصال
        contact_links = soup.find_all('a')
        for link in contact_links:
            href = link.get('href', '')
            text = link.get_text()
            
            if href.startswith('tel:'):
                phone_match = re.search(r'الهاتف\s*الرسمي\s*:?\s*(.+)', text)
                if phone_match:
                    charity_info['الهاتف'] = clean_text(phone_match.group(1))
                    
            elif href.startswith('mailto:'):
                email_match = re.search(r'البريد\s*الالكتروني\s*:?\s*(.+)', text)
                if email_match:
                    charity_info['البريد الإلكتروني'] = clean_text(email_match.group(1))
                    
            elif href.startswith('http') and 'الموقع الالكتروني' in text:
                website_match = re.search(r'الموقع\s*الالكتروني\s*:?\s*(.+)', text)
                if website_match:
                    charity_info['الموقع الإلكتروني'] = clean_text(website_match.group(1))
                    
            elif href.startswith('fax:'):
                fax_match = re.search(r'الفاكس\s*:?\s*(.+)', text)
                if fax_match:
                    charity_info['الفاكس'] = clean_text(fax_match.group(1))
                    
            elif 'ص.ب' in text:
                po_box_match = re.search(r'ص\.ب\s*:?\s*(.+)', text)
                if po_box_match:
                    charity_info['صندوق البريد'] = clean_text(po_box_match.group(1))

        # التأكد من وجود القيم الافتراضية
        default_fields = ['العنوان', 'الهاتف', 'الفاكس', 'الموقع الإلكتروني', 'البريد الإلكتروني', 'صندوق البريد']
        for field in default_fields:
            if field not in charity_info:
                charity_info[field] = 'غير متوفر'

        return charity_info

    except Exception as e:
        print(f"  -> خطأ أثناء سحب التفاصيل: {e}")
        return None

def main():
    """الدالة الرئيسية للاختبار السريع"""
    print("=" * 60)
    print("اختبار سريع لسحب بيانات الجمعيات")
    print("=" * 60)
    
    # تحديد عدد الصفحات للاختبار
    num_pages = 1  # صفحة واحدة فقط للاختبار السريع
    all_charities_data = []
    
    print(f"سيتم سحب البيانات من {num_pages} صفحة للاختبار")
    print("=" * 60)
    
    for page_num in range(1, num_pages + 1):
        page_url = f"{BASE_URL}?page={page_num}"
        print(f"\n===== جاري العمل على الصفحة رقم: {page_num} =====")
        
        try:
            response = requests.get(page_url, headers=HEADERS, timeout=20)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'lxml')
            
            # البحث عن الجمعيات
            charity_cards = soup.find_all('div', class_='main-card')
            print(f"تم العثور على {len(charity_cards)} جمعية في الصفحة.")
            
            if not charity_cards:
                print("لم يتم العثور على جمعيات في هذه الصفحة.")
                continue
            
            # معالجة أول 3 جمعيات فقط للاختبار السريع
            for card_idx, card in enumerate(charity_cards[:3]):
                try:
                    print(f"معالجة الجمعية رقم {card_idx + 1}...")
                    
                    # البحث عن رابط المزيد
                    more_link_element = card.find('a', string=re.compile(r'\s*المزيد\s*'))
                    
                    if not more_link_element:
                        links = card.find_all('a')
                        for link in links:
                            href = link.get('href', '')
                            if href and '/nonprofits/' in href and href.split('/')[-1].isdigit():
                                more_link_element = link
                                break
                    
                    if more_link_element and more_link_element.get('href'):
                        detail_url = more_link_element['href']
                        
                        if not detail_url.startswith('http'):
                            base_domain = re.match(r'(https?://[^/]+)', BASE_URL).group(1)
                            detail_url = f"{base_domain}{detail_url}"
                        
                        # سحب البيانات
                        charity_data = scrape_details(detail_url, HEADERS)
                        if charity_data:
                            all_charities_data.append(charity_data)
                            print(f"  ✓ تم سحب بيانات: {charity_data.get('الاسم', 'بدون اسم')}")
                        
                        time.sleep(1)  # تأخير قصير
                        
                except Exception as e:
                    print(f"  × خطأ عند معالجة جمعية: {e}")
                    
        except Exception as e:
            print(f"خطأ في الوصول للصفحة {page_num}: {e}")
    
    # حفظ البيانات
    if all_charities_data:
        print(f"\n... انتهى الاختبار، جاري حفظ {len(all_charities_data)} جمعية ...")
        
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        
        df = pd.DataFrame(all_charities_data)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{results_dir}/quick_test_{timestamp}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✓ تم حفظ البيانات في: {filename}")
        
        # طباعة ملخص
        print("\n" + "=" * 60)
        print("ملخص النتائج:")
        print("=" * 60)
        for idx, charity in enumerate(all_charities_data, 1):
            print(f"{idx}. {charity.get('الاسم', 'بدون اسم')}")
            print(f"   النوع: {charity.get('المجال', 'غير متوفر')}")
            print(f"   الرقم: {charity.get('رقم التسجيل', 'غير متوفر')}")
            print(f"   الهاتف: {charity.get('الهاتف', 'غير متوفر')}")
            print()
    else:
        print("لم يتم سحب أي بيانات!")

if __name__ == "__main__":
    main()
