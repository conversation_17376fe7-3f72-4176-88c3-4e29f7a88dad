#!/bin/bash

# ملف تشغيل بسيط لسحب بيانات الجمعيات

echo "========================================================"
echo "🏛️  سحب بيانات الجمعيات من موقع المركز الوطني"
echo "========================================================"
echo ""

# التحقق من وجود البيئة الافتراضية
if [ -d "venv" ]; then
    echo "✅ تم العثور على البيئة الافتراضية"
    source venv/bin/activate
else
    echo "❌ لم يتم العثور على البيئة الافتراضية"
    echo "يرجى تشغيل: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

echo ""
echo "اختر نوع العملية:"
echo "1) اختبار سريع (3 جمعيات فقط)"
echo "2) اختبار الاتصال"
echo "3) سحب 5 صفحات (25 جمعية)"
echo "4) عرض النتائج المحفوظة"
echo ""

read -p "اختر رقم العملية (1-4): " choice

case $choice in
    1)
        echo "🚀 تشغيل الاختبار السريع..."
        python3 quick_test.py
        ;;
    2)
        echo "🚀 تشغيل اختبار الاتصال..."
        python3 test_scraper.py
        ;;
    3)
        echo "🚀 تشغيل سحب 5 صفحات..."
        # تعديل مؤقت للملف
        sed -i.bak 's/last_page = 10/last_page = 5/' scrap.py
        python3 scrap.py
        # استعادة الملف الأصلي
        mv scrap.py.bak scrap.py
        ;;
    4)
        echo "📁 النتائج المحفوظة:"
        echo "------------------------"
        if [ -d "results" ]; then
            ls -la results/*.csv 2>/dev/null || echo "لا توجد ملفات CSV"
        else
            echo "لا يوجد مجلد نتائج"
        fi
        ;;
    *)
        echo "❌ اختيار غير صحيح"
        ;;
esac

echo ""
echo "✅ انتهت العملية"
