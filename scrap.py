import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import os
from datetime import datetime

# --- الإعدادات الأساسية ---
BASE_URL = "https://es.ncnp.gov.sa/v5/nonprofits"
# نستخدم Headers لنبدو كمتصفح حقيقي
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
    'Referer': 'https://es.ncnp.gov.sa/',
    'Sec-Ch-Ua': '"Google Chrome";v="123", "Not:A=Brand";v="8", "Chromium";v="123"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1'
}
# قائمة لتخزين بيانات كل الجمعيات
all_charities_data = []

def save_progress(data, page_num):
    """
    حفظ البيانات بشكل تدريجي لتجنب فقدان البيانات في حالة حدوث خطأ
    """
    if not data:
        return

    try:
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)

        # حفظ البيانات الحالية
        df = pd.DataFrame(data)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{results_dir}/progress_page_{page_num}_{timestamp}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"تم حفظ التقدم: {len(data)} جمعية في الملف {filename}")

    except Exception as e:
        print(f"خطأ في حفظ التقدم: {e}")

def clean_text(text):
    """
    تنظيف النصوص من المسافات الزائدة والأحرف غير المرغوبة
    """
    if not text or text == 'غير متوفر':
        return 'غير متوفر'

    # إزالة المسافات الزائدة والأسطر الجديدة
    text = re.sub(r'\s+', ' ', text.strip())
    # إزالة الأحرف الخاصة غير المرغوبة
    text = re.sub(r'[^\w\s\u0600-\u06FF@.\-+():/]', '', text)
    return text.strip() if text.strip() else 'غير متوفر'

def get_last_page_number(url, headers):
    """
    هذه الدالة تقوم بزيارة الصفحة الأولى لتحديد رقم آخر صفحة بشكل تلقائي.
    """
    try:
        response = requests.get(url, headers=headers, timeout=20)
        response.raise_for_status()

        # إنشاء مجلد النتائج إذا لم يكن موجوداً
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)

        # حفظ محتوى الصفحة الأولى للتحليل
        with open('results/first_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("تم حفظ محتوى الصفحة الأولى في ملف 'results/first_page.html' للمساعدة في التحليل.")

        soup = BeautifulSoup(response.content, 'lxml')

        # طرق متعددة للبحث عن الترقيم
        pagination_selectors = [
            'ul.pagination li.page-item a.page-link',
            'ul.pagination a',
            '[class*="pagination"] a',
            '.pagination a',
            '.page-numbers a',
            '.pager a',
            'nav a[href*="page"]'
        ]

        pagination_links = []
        for selector in pagination_selectors:
            pagination_links = soup.select(selector)
            if pagination_links:
                break

        # البحث في النص العام للصفحة عن معلومات الترقيم
        page_info_patterns = [
            r'صفحة\s+\d+\s+من\s+(\d+)',
            r'Page\s+\d+\s+of\s+(\d+)',
            r'(\d+)\s+صفحة',
            r'إجمالي\s*:\s*(\d+)\s*صفحة',
            r'Total\s*:\s*(\d+)\s*pages?'
        ]

        page_numbers = []

        # استخراج الأرقام من روابط الترقيم
        for link in pagination_links:
            text = link.text.strip()
            href = link.get('href', '')

            # البحث في النص
            matches = re.findall(r'\d+', text)
            if matches:
                for match in matches:
                    try:
                        page_numbers.append(int(match))
                    except ValueError:
                        continue

            # البحث في الرابط
            href_matches = re.findall(r'page[=:](\d+)', href)
            if href_matches:
                for match in href_matches:
                    try:
                        page_numbers.append(int(match))
                    except ValueError:
                        continue

        # البحث في النص العام للصفحة
        page_text = soup.get_text()
        for pattern in page_info_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                for match in matches:
                    try:
                        page_numbers.append(int(match))
                    except ValueError:
                        continue

        if page_numbers:
            max_page = max(page_numbers)
            print(f"تم العثور على {max_page} صفحة")
            return max_page
        else:
            print("لم يتم العثور على أرقام الصفحات، سيتم سحب الصفحة الأولى فقط.")
            return 1

    except requests.exceptions.RequestException as e:
        print(f"خطأ في الاتصال بالصفحة الأولى: {e}")
        return 1
    except Exception as e:
        print(f"خطأ غير متوقع في تحديد عدد الصفحات: {e}")
        return 1

def scrape_details(detail_url, headers):
    """
    هذه الدالة تقوم بسحب البيانات التفصيلية من صفحة الجمعية.
    """
    try:
        print(f"  -> جاري سحب تفاصيل من: {detail_url}")
        response = requests.get(detail_url, headers=headers, timeout=20)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'lxml')

        charity_info = {}

        # سحب اسم الجمعية من الهيكل الصحيح
        name_element = soup.find('div', class_='card-title')
        if not name_element:
            # البحث في main-info-details
            name_element = soup.select_one('.main-info-details h5')
        if not name_element:
            # البحث في أي h5 أو h4
            name_element = soup.find('h5') or soup.find('h4')

        if name_element:
            # إزالة النص الخاص بالنوع (span.tag) إذا كان موجوداً
            name_text = name_element.get_text()
            # إزالة النوع من النص
            name_text = re.sub(r'جمعية أهلية|مؤسسة أهلية|صندوق عائلة', '', name_text).strip()
            charity_info['الاسم'] = clean_text(name_text)
        else:
            charity_info['الاسم'] = 'غير متوفر'

        # سحب النوع/المجال من span.tag
        category_element = soup.find('span', class_='tag')
        if not category_element:
            category_element = soup.find('span', class_='light tag')
        charity_info['المجال'] = clean_text(category_element.text) if category_element else 'غير متوفر'

        # سحب رقم الترخيص من النص المحدد
        license_text = soup.get_text()
        reg_num_match = re.search(r'رقم\s*الترخيص\s*:?\s*(\d+)', license_text)
        if not reg_num_match:
            reg_num_match = re.search(r'رقم\s*التسجيل\s*:?\s*(\d+)', license_text)
        if not reg_num_match:
            reg_num_match = re.search(r'الترخيص\s*:?\s*(\d+)', license_text)
        charity_info['رقم التسجيل'] = reg_num_match.group(1) if reg_num_match else 'غير متوفر'

        # سحب العنوان من الفقرة المحددة
        address_paragraphs = soup.find_all('p')
        for p in address_paragraphs:
            text = p.get_text()
            if 'منطقة' in text or 'الرمز البريدي' in text:
                charity_info['العنوان'] = clean_text(text)
                break

        # سحب جهة الإشراف
        for p in address_paragraphs:
            text = p.get_text()
            if 'جهة الاشراف' in text:
                supervisor_text = re.sub(r'جهة الاشراف\s*:?\s*', '', text).strip()
                charity_info['جهة الإشراف'] = clean_text(supervisor_text)
                break

        # سحب معلومات الاتصال من الروابط
        contact_links = soup.find_all('a')
        for link in contact_links:
            href = link.get('href', '')
            text = link.get_text()

            if href.startswith('tel:'):
                phone_match = re.search(r'الهاتف\s*الرسمي\s*:?\s*(.+)', text)
                if phone_match:
                    charity_info['الهاتف'] = clean_text(phone_match.group(1))

            elif href.startswith('mailto:'):
                email_match = re.search(r'البريد\s*الالكتروني\s*:?\s*(.+)', text)
                if email_match:
                    charity_info['البريد الإلكتروني'] = clean_text(email_match.group(1))

            elif href.startswith('http') and 'الموقع الالكتروني' in text:
                website_match = re.search(r'الموقع\s*الالكتروني\s*:?\s*(.+)', text)
                if website_match:
                    charity_info['الموقع الإلكتروني'] = clean_text(website_match.group(1))

            elif href.startswith('fax:'):
                fax_match = re.search(r'الفاكس\s*:?\s*(.+)', text)
                if fax_match:
                    charity_info['الفاكس'] = clean_text(fax_match.group(1))

            elif 'ص.ب' in text:
                po_box_match = re.search(r'ص\.ب\s*:?\s*(.+)', text)
                if po_box_match:
                    charity_info['صندوق البريد'] = clean_text(po_box_match.group(1))

        # التأكد من وجود القيم الافتراضية للحقول المفقودة
        default_fields = ['العنوان', 'الهاتف', 'الفاكس', 'الموقع الإلكتروني', 'البريد الإلكتروني', 'صندوق البريد', 'جهة الإشراف']
        for field in default_fields:
            if field not in charity_info:
                charity_info[field] = 'غير متوفر'

        return charity_info

    except requests.exceptions.RequestException as e:
        print(f"  -> خطأ أثناء سحب التفاصيل من {detail_url}: {e}")
        return None
    except Exception as e:
        print(f"  -> خطأ غير متوقع أثناء معالجة {detail_url}: {e}")
        return None

# --- بداية عمل البرنامج ---

# 1. تحديد عدد الصفحات
print("بدء عملية سحب البيانات...")
print("=" * 50)

# ملحوظة: الموقع ضخم جداً (أكثر من 1200 صفحة). للسحب الكامل، استخدم السطر الأول.
# للاختبار، استخدم السطر الثاني لسحب أول 3 صفحات فقط.
try:
    last_page = get_last_page_number(BASE_URL, HEADERS)
    # للتجربة، يمكنك تحديد عدد أقل من الصفحات
    # last_page = min(last_page, 10)  # مثال: سحب أول 10 صفحات فقط
    print(f"تم اكتشاف {last_page} صفحة في الموقع")

    # تأكيد من المستخدم للسحب الكامل
    if last_page > 50:
        print(f"تحذير: الموقع يحتوي على {last_page} صفحة، مما يعني آلاف الجمعيات!")
        print("هذا قد يستغرق ساعات عديدة...")
        print("يُنصح بالبدء بعدد أقل للاختبار")
        # تحديد عدد افتراضي للاختبار
        last_page = 10
        print(f"تم تحديد {last_page} صفحة للاختبار")

except Exception as e:
    print(f"خطأ في تحديد عدد الصفحات: {e}")
    last_page = 5  # قيمة افتراضية للاختبار

print(f"سيتم سحب البيانات من {last_page} صفحة.")
print("=" * 50)

# 2. المرور على كل الصفحات
for page_num in range(1, last_page + 1):
    page_url = f"{BASE_URL}?page={page_num}"
    print(f"\n===== جاري العمل على الصفحة رقم: {page_num} =====")
    
    try:
        main_response = requests.get(page_url, headers=HEADERS, timeout=20)
        main_response.raise_for_status()
        main_soup = BeautifulSoup(main_response.content, 'lxml')
        
        # طباعة أول جزء من محتوى الصفحة للتحقق (للتشخيص فقط)
        page_content_preview = main_response.text[:500]
        print("محتوى الصفحة (الجزء الأول):", page_content_preview)
        
        # 3. البحث عن الجمعيات بناءً على الهيكل الصحيح للموقع
        charity_cards = main_soup.find_all('div', class_='main-card')

        print(f"تم العثور على {len(charity_cards)} جمعية في الصفحة.")
        
        if not charity_cards:
            print("لم يتم العثور على جمعيات في هذه الصفحة.")
            continue

        # 4. المرور على كل جمعية لسحب رابط التفاصيل
        for card_idx, card in enumerate(charity_cards):  # معالجة جميع الجمعيات في الصفحة
            try:
                print(f"معالجة الجمعية رقم {card_idx + 1}...")

                # البحث عن رابط "المزيد" في الهيكل الصحيح
                more_link_element = None

                # البحث عن رابط المزيد بناءً على النص "المزيد"
                more_link_element = card.find('a', string=re.compile(r'\s*المزيد\s*'))

                # إذا لم نجد، نبحث في جميع الروابط
                if not more_link_element:
                    links = card.find_all('a')
                    for link in links:
                        href = link.get('href', '')
                        # البحث عن روابط تحتوي على nonprofits مع رقم
                        if href and '/nonprofits/' in href and href.split('/')[-1].isdigit():
                            more_link_element = link
                            break

                # طباعة معلومات للتشخيص
                if more_link_element:
                    print(f"  تم العثور على رابط: {more_link_element.get('href', 'بدون رابط')}")
                else:
                    print("  لم يتم العثور على رابط المزيد")
                    # طباعة جزء من محتوى الكارد للتشخيص
                    card_text = card.get_text()[:100].replace('\n', ' ').strip()
                    print(f"  محتوى الكارد: {card_text}...")
                    continue
                
                # التحقق من وجود رابط صحيح ومعالجته
                if more_link_element and more_link_element.get('href'):
                    detail_url = more_link_element['href']

                    # التأكد من أن الرابط كامل
                    if not detail_url.startswith('http'):
                        base_domain = re.match(r'(https?://[^/]+)', BASE_URL).group(1)
                        detail_url = f"{base_domain}{detail_url}"

                    print(f"  -> سحب البيانات من: {detail_url}")

                    # 5. سحب البيانات من صفحة التفاصيل
                    charity_data = scrape_details(detail_url, HEADERS)
                    if charity_data:
                        all_charities_data.append(charity_data)
                        print(f"  ✓ تم سحب بيانات: {charity_data.get('الاسم', 'بدون اسم')}")
                    else:
                        print(f"  × فشل في سحب البيانات من {detail_url}")

                    # إضافة تأخير بسيط بين الطلبات لعدم إرهاق الخادم
                    time.sleep(2)

            except Exception as e:
                print(f"  × خطأ عند معالجة بيانات جمعية: {e}")
                continue

        # حفظ التقدم بعد كل صفحة
        if all_charities_data:
            save_progress(all_charities_data, page_num)

    except requests.exceptions.RequestException as e:
        print(f"حدث خطأ في الوصول للصفحة {page_num}: {e}")
        continue

# 6. حفظ البيانات في ملف CSV
if all_charities_data:
    print("\n... انتهت عملية السحب، جاري حفظ البيانات ...")
    df = pd.DataFrame(all_charities_data)
    
    # تحديد الأعمدة بالترتيب المرغوب
    columns_order = ['الاسم', 'رقم التسجيل', 'المجال', 'العنوان', 'الهاتف', 'الفاكس', 
                    'الموقع الإلكتروني', 'البريد الإلكتروني', 'صندوق البريد']
    
    # التأكد من وجود الأعمدة قبل إعادة ترتيبها
    df = df.reindex(columns=[col for col in columns_order if col in df.columns])
    
    # إنشاء مجلد للنتائج إذا لم يكن موجوداً
    results_dir = "results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
        
    # إضافة التاريخ والوقت لاسم الملف
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{results_dir}/charities_data_{timestamp}.csv"
    
    # الحفظ مع ترميز utf-8-sig ليفتح الملف بالعربية بشكل صحيح في Excel
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"تم حفظ البيانات بنجاح في ملف '{filename}'")
    print(f"عدد الجمعيات التي تم سحبها: {len(df)}")
    
    # حفظ نسخة من البيانات بتنسيق Excel أيضاً
    excel_filename = f"{results_dir}/charities_data_{timestamp}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    print(f"تم حفظ البيانات أيضاً بصيغة إكسل: '{excel_filename}'")
else:
    print("لم يتم سحب أي بيانات.")